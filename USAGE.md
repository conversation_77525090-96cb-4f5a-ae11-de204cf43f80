# 视频 & 音频速度控制器使用指南

本文档提供了视频 & 音频速度控制器Chrome扩展的详细使用说明。

## 目录

- [安装指南](#安装指南)
- [基本功能](#基本功能)
- [快捷键设置](#快捷键设置)
- [网页全屏模式](#网页全屏模式)
- [常见问题](#常见问题)
- [高级技巧](#高级技巧)
- [故障排除](#故障排除)

## 安装指南

### 从Chrome网上应用店安装

1. 访问[Chrome网上应用店](https://chrome.google.com/webstore/category/extensions)（链接待更新）
2. 搜索"视频 & 音频速度控制器"或直接访问我们的扩展页面
3. 点击"添加到Chrome"按钮
4. 在弹出的确认对话框中点击"添加扩展程序"
5. 安装完成后，您会在Chrome工具栏看到我们的图标

### 手动安装开发版

1. 从GitHub下载最新版本的代码库
2. 解压下载的文件到本地文件夹
3. 打开Chrome浏览器，在地址栏输入 `chrome://extensions/`
4. 在右上角启用"开发者模式"
5. 点击"加载已解压的扩展程序"按钮
6. 选择您解压的文件夹
7. 扩展会立即安装并启用

## 基本功能

### 速度控制

本扩展的核心功能是控制网页上视频和音频的播放速度。默认快捷键如下：

- **增加速度**：按下 `=` 键，每次增加0.1倍速
- **减小速度**：按下 `-` 键，每次减少0.1倍速
- **重置速度**：按下 `0` 键，将速度重置为1.0倍（正常速度）

### 播放控制

除了速度控制，本扩展还提供基本的媒体播放控制：

- **播放/暂停**：按下 `空格键` 切换当前媒体的播放状态
- **网页全屏**：按下 `f` 键进入或退出网页全屏模式

### 速度指示器

当您更改播放速度时，屏幕上会显示一个临时的速度指示器，显示当前的播放速度。此指示器会在1.5秒后自动消失。

### 目标媒体选择

当页面上有多个视频或音频元素时，扩展会按以下优先顺序选择控制的目标：

1. 如果处于网页全屏模式，则控制全屏的视频
2. 最近鼠标悬停或点击过的媒体元素
3. 当前在视口中且尺寸最大的媒体元素
4. 页面上第一个找到的媒体元素

## 快捷键设置

### 自定义快捷键

您可以根据个人喜好自定义所有快捷键：

1. 点击Chrome工具栏上的扩展图标，打开设置面板
2. 在"快捷键"标签下，您会看到所有可自定义的操作
3. 点击对应的输入框，然后按下您想要设置的键或键组合
4. 系统会自动检测并显示您按下的键
5. 设置完成后点击"保存"按钮应用更改

### 快捷键冲突检测

设置界面会自动检测以下问题：

- 重复的快捷键设置
- 空白的快捷键设置
- 与浏览器常用快捷键的冲突

如果检测到任何问题，输入框会变为红色警告状态，并在底部显示具体的警告信息。

### 重置默认设置

如果您想恢复默认快捷键设置：

1. 打开设置面板
2. 点击"恢复默认设置"按钮
3. 点击"保存"按钮应用更改

## 网页全屏模式

### 什么是网页全屏？

网页全屏是本扩展提供的特殊功能，不同于浏览器的原生全屏模式。它会在当前网页内创建一个全屏的视频观看环境，有以下特点：

- 视频会居中显示并自动调整到最佳大小
- 背景变为黑色，减少干扰
- 保留原视频的控制条
- 可以使用Esc键或再次按F键退出

### 使用方法

1. 在任何包含视频的页面，使用鼠标悬停或点击选中目标视频
2. 按下 `f` 键进入网页全屏模式
3. 再次按 `f` 键或按 `Esc` 键退出网页全屏模式

### 网页全屏的优势

- 不受网站原有UI的干扰
- 保留浏览器标签栏和地址栏，方便切换
- 所有快捷键在全屏模式下仍然有效
- 比浏览器原生全屏更灵活

## 常见问题

### 为什么有些网站上快捷键不起作用？

某些网站可能会拦截键盘事件或使用与我们扩展相同的快捷键。解决方法：

1. 尝试进入网页全屏模式后再使用快捷键
2. 在设置中将快捷键更改为不常用的组合
3. 点击视频区域确保其获得焦点后再使用快捷键

### 为什么在网页全屏模式下点击控制条后快捷键失效？

这是因为视频控制条可能会捕获键盘焦点。解决方法：

1. 点击视频内容区域而非控制条
2. 等待几秒钟控制条自动隐藏后再使用快捷键
3. 使用Tab键将焦点从控制条移开

### 是否支持控制所有标签页中的视频？

不支持。出于安全考虑，Chrome扩展只能控制当前活跃标签页中的媒体元素。

## 高级技巧

### 在视频会议中使用

在Zoom、Google Meet等网页版视频会议中，您可以：

1. 使用此扩展加速冗长的会议回放
2. 在长时间的演讲中调整速度以节省时间
3. 对于不同的发言者使用不同的播放速度

### 在在线课程中使用

对于在线教育平台如Coursera、Udemy等：

1. 根据内容难度调整速度，简单内容可以加速
2. 遇到复杂概念时可以减速或暂停
3. 使用网页全屏模式减少平台UI干扰，专注学习

### 提高检测效率的技巧

如果扩展未能自动检测到视频：

1. 尝试刷新页面
2. 直接点击视频使其获得焦点
3. 对于动态加载的内容，等待内容完全加载后再使用快捷键

## 故障排除

### 扩展图标点击无反应

1. 确保Chrome浏览器已更新到最新版本
2. 右键点击扩展图标，选择"管理扩展"
3. 确保扩展已启用
4. 尝试重新安装扩展

### 速度变化无效

1. 确保您选择的是可控制的媒体元素
2. 某些网站可能限制了播放速度的修改，尝试其他网站测试
3. 检查控制台是否有错误信息

### 网页全屏模式显示异常

1. 确保浏览器允许扩展修改页面样式
2. 某些网站可能使用特殊的视频播放器，可能与网页全屏不完全兼容
3. 尝试退出并重新进入网页全屏模式

---

如果您遇到本文档未涵盖的问题，请通过GitHub Issues报告，或发送邮件至*******************（请替换为实际联系方式）。

感谢您使用视频 & 音频速度控制器！ 