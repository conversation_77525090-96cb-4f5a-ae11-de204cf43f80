# 视频 & 音频速度控制器 (Video & Audio Speed Controller)

一个强大的 Chrome 浏览器扩展，允许您使用键盘快捷键轻松控制网页上视频和音频的播放速度。专为在线学习、视频会议和媒体爱好者设计。

![图标](icons/icon128.png)

## 功能特点

- 🚀 **自定义快捷键**：可定制的键盘快捷键控制媒体播放
- 🎯 **智能媒体检测**：自动检测页面上的视频和音频元素，包括动态加载的内容
- 🌐 **跨站点兼容**：适用于几乎所有网站，包括 YouTube、Bilibili、网课平台等
- 🌈 **视觉反馈**：调整速度时显示美观的屏幕提示
- 🎭 **网页全屏模式**：一键进入沉浸式观看模式，不受网站原有 UI 干扰
- 🔍 **Shadow DOM 支持**：能够检测和控制 Shadow DOM 中的媒体元素
- 🧩 **iframe 支持**：控制嵌入在 iframe 中的媒体
- 📱 **响应式设计**：在各种屏幕尺寸下都能正常工作
- 🔋 **高性能设计**：极低的资源占用，不影响浏览体验

## 默认快捷键

| 功能      | 快捷键   | 描述                        |
| --------- | -------- | --------------------------- |
| 加速      | `=`      | 增加播放速度 (+0.1)         |
| 减速      | `-`      | 降低播放速度 (-0.1)         |
| 重置速度  | `0`      | 将速度重置为 1.0x(正常速度) |
| 播放/暂停 | `空格键` | 切换媒体的播放/暂停状态     |
| 网页全屏  | `f`      | 进入/退出网页全屏模式       |

> 所有快捷键均可在扩展选项中自定义

## 安装方法

### 从 Chrome 网上应用店安装（推荐）

1. 访问[Chrome 网上应用店](https://chrome.google.com/webstore/category/extensions)（链接待更新）
2. 点击"添加到 Chrome"按钮
3. 确认安装

### 手动安装（开发版）

1. 下载或克隆此仓库到本地
2. 打开 Chrome 浏览器，访问 `chrome://extensions/`
3. 启用右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择包含此项目文件的文件夹

## 使用说明

### 基本使用

1. 安装扩展后，访问任何包含视频或音频的网页
2. 使用默认快捷键（或您自定义的快捷键）控制媒体播放
3. 调整速度时，屏幕上会短暂显示当前速度

### 视频选择逻辑

当页面上有多个媒体元素时，扩展会按以下优先级选择目标媒体：

1. 网页全屏模式中的视频
2. 最近鼠标悬停或点击的媒体
3. 当前在视口中且尺寸最大的媒体
4. 页面上第一个找到的媒体元素

### 自定义快捷键

1. 点击扩展图标打开设置页面
2. 在表单中设置您喜欢的快捷键
3. 点击"保存"按钮应用更改

### 常见问题解答

**Q: 快捷键在某些网站上不工作？**  
A: 一些网站可能会覆盖全局键盘快捷键。尝试使用网页全屏模式（按`f`）后再使用快捷键，或者在设置中更改为不常用的快捷键组合。

**Q: 在网页全屏模式下，点击视频控制条后快捷键无效？**  
A: 这是已知问题，我们在最新版本中已修复。如果仍然遇到此问题，请尝试点击视频内容区域而非控制条。

**Q: 为什么不能设置全局快捷键？**  
A: 出于安全考虑，Chrome 扩展不允许在未激活的标签页中运行内容脚本。我们只能控制当前活跃标签页中的媒体。

## 技术特点

- **高性能设计**：使用优化的缓存系统和防抖技术，确保在复杂页面上也能流畅运行
- **增强的媒体检测**：使用递减间隔检查和 MutationObserver，精确捕获动态加载的媒体
- **安全的事件处理**：采用事件委托和捕获阶段监听，确保快捷键在各种情况下都能正常工作
- **IntersectionObserver 支持**：使用现代 API 优化元素可见性检测
- **轻量级**：代码体积小，对浏览器性能影响最小

## 项目结构

```
chrome-plugin-video-manager/
  ├── manifest.json     # 扩展配置文件
  ├── content.js        # 主要功能实现
  ├── background.js     # 后台服务
  ├── options.html      # 设置页面HTML
  ├── options.js        # 设置页面逻辑
  ├── style.css         # 样式表
  └── icons/            # 扩展图标
      ├── icon16.png
      ├── icon48.png
      └── icon128.png
```

## 开发环境

- 本项目是纯原生 JavaScript 项目，无需构建工具
- 开发时请使用 Chrome 浏览器的开发者模式加载扩展
- 调试可使用 Chrome 开发者工具的"检查扩展视图"功能

## 隐私说明

- 此扩展不收集任何用户数据
- 不需要网络访问权限
- 所有设置都存储在本地浏览器中

## 未来计划

- [ ] 添加自定义步进值（控制每次加减速的幅度）
- [ ] 支持更多视频操作（如跳过、循环特定片段）
- [ ] 为常用网站添加特定优化
- [ ] 添加快捷键冲突检测与提示
- [ ] 支持更多浏览器（Firefox、Edge 等）

## 已知问题

- 在某些使用严格 CSP(内容安全策略)的网站上，样式可能无法正常加载
- 极少数使用非标准视频播放器的网站可能无法被检测到
- 某些 SPA(单页应用)在页面切换时可能需要刷新才能检测到新视频

## 贡献指南

欢迎贡献代码、报告问题或提出功能建议！

1. Fork 这个仓库
2. 创建您的特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交您的更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 打开一个 Pull Request

## 许可证

此项目采用 MIT 许可证 - 详情请查看[LICENSE](LICENSE)文件

## 支持与反馈

如有任何问题或建议，请在 GitHub 仓库中提交 Issue，或通过以下方式联系我们：

- Email: <EMAIL>（请替换为实际联系方式）

---

**开发者:** [Your Name](https://github.com/yourusername)  
**版本:** 1.3.2
