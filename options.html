<!DOCTYPE html>
<html>
<head>
    <title>Video Speed Controller - 视频速度控制器</title>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            padding: 10px 20px;
            background-color: #f0f2f5;
            min-width: 300px;
        }
        h1 {
            font-size: 18px;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"] {
            width: 100px;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        button {
            padding: 10px 15px;
            border: none;
            background-color: #007bff;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #status {
            margin-top: 15px;
            color: green;
            font-weight: bold;
        }
        .help-text {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            font-size: 14px;
            line-height: 1.5;
        }
        .shortcut-description {
            color: #666;
            font-size: 12px;
            font-style: italic;
            margin-top: 3px;
        }
        .tab-container {
            margin-top: 20px;
        }
        .tab-buttons {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 15px;
        }
        .tab-button {
            padding: 8px 16px;
            cursor: pointer;
            background: #f0f0f0;
            border: 1px solid #ddd;
            border-bottom: none;
            margin-right: 5px;
            border-radius: 4px 4px 0 0;
        }
        .tab-button.active {
            background: #fff;
            border-bottom: 1px solid #fff;
            margin-bottom: -1px;
            font-weight: bold;
        }
        .tab-content {
            display: none;
            padding: 15px;
            background: #fff;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 4px 4px;
        }
        .tab-content.active {
            display: block;
        }
        code {
            background-color: #f1f1f1;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
        .bilingual {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .bilingual .en {
            color: #666;
            font-size: 0.9em;
        }
        .description-bilingual {
            display: flex;
            justify-content: space-between;
        }
        .description-bilingual .en {
            color: #888;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <h1 class="bilingual">
        <span class="zh">视频速度控制器设置</span>
        <span class="en">Video Speed Controller Settings</span>
    </h1>
    
    <div class="tab-container">
        <div class="tab-buttons">
            <div class="tab-button active" data-tab="shortcuts">
                <span class="bilingual">
                    <span class="zh">快捷键</span>
                    <span class="en">Shortcuts</span>
                </span>
            </div>
            <div class="tab-button" data-tab="help">
                <span class="bilingual">
                    <span class="zh">使用帮助</span>
                    <span class="en">Help</span>
                </span>
            </div>
        </div>
        
        <div id="shortcuts" class="tab-content active">
            <div class="form-group">
                <label for="increase" class="bilingual">
                    <span class="zh">加速:</span>
                    <span class="en">Speed Up:</span>
                </label>
                <input type="text" id="increase" class="shortcut-input">
                <div class="description-bilingual">
                    <span class="zh shortcut-description">增加视频播放速度 (+0.1)</span>
                    <span class="en shortcut-description">Increase playback speed (+0.1)</span>
                </div>
            </div>

            <div class="form-group">
                <label for="decrease" class="bilingual">
                    <span class="zh">减速:</span>
                    <span class="en">Slow Down:</span>
                </label>
                <input type="text" id="decrease" class="shortcut-input">
                <div class="description-bilingual">
                    <span class="zh shortcut-description">降低视频播放速度 (-0.1)</span>
                    <span class="en shortcut-description">Decrease playback speed (-0.1)</span>
                </div>
            </div>

            <div class="form-group">
                <label for="reset" class="bilingual">
                    <span class="zh">重置速度:</span>
                    <span class="en">Reset Speed:</span>
                </label>
                <input type="text" id="reset" class="shortcut-input">
                <div class="description-bilingual">
                    <span class="zh shortcut-description">将播放速度重置为 1.0x (正常速度)</span>
                    <span class="en shortcut-description">Reset playback speed to 1.0x (normal)</span>
                </div>
            </div>



            <div class="form-group">
                <label for="toggle-fullscreen" class="bilingual">
                    <span class="zh">切换网页全屏:</span>
                    <span class="en">Toggle Fullscreen:</span>
                </label>
                <input type="text" id="toggle-fullscreen" class="shortcut-input">
                <div class="description-bilingual">
                    <span class="zh shortcut-description">进入/退出网页全屏模式（非浏览器全屏）</span>
                    <span class="en shortcut-description">Enter/exit page fullscreen mode</span>
                </div>
            </div>

            <div style="display: flex; gap: 10px;">
                <button id="save" class="bilingual">
                    <span class="zh">保存</span>
                    <span class="en">Save</span>
                </button>
                <button id="reset-shortcuts" style="background-color: #6c757d;" class="bilingual">
                    <span class="zh">恢复默认设置</span>
                    <span class="en">Reset to Default</span>
                </button>
            </div>
            <div id="status"></div>
        </div>
        
        <div id="help" class="tab-content">
            <h3 class="bilingual">
                <span class="zh">使用说明</span>
                <span class="en">Instructions</span>
            </h3>
            <p class="bilingual">
                <span class="zh">本插件允许您使用自定义快捷键控制网页上视频和音频的播放速度。</span>
                <span class="en">This extension allows you to control video and audio playback speed using custom shortcuts.</span>
            </p>
            
            <h4 class="bilingual">
                <span class="zh">基本使用方法</span>
                <span class="en">Basic Usage</span>
            </h4>
            <ul>
                <li class="bilingual">
                    <span class="zh">插件会自动检测当前页面上的视频或音频元素</span>
                    <span class="en">Extension automatically detects video or audio elements on the page</span>
                </li>
                <li class="bilingual">
                    <span class="zh">使用设置的快捷键直接控制媒体播放</span>
                    <span class="en">Use configured shortcuts to control media playback</span>
                </li>
                <li class="bilingual">
                    <span class="zh">更改播放速度时，会显示速度指示器</span>
                    <span class="en">Speed indicator will appear when changing playback speed</span>
                </li>
            </ul>
            
            <h4 class="bilingual">
                <span class="zh">视频选择逻辑</span>
                <span class="en">Video Selection Logic</span>
            </h4>
            <ul>
                <li class="bilingual">
                    <span class="zh">插件优先控制您鼠标悬停的视频/音频</span>
                    <span class="en">Extension prioritizes video/audio under your mouse cursor</span>
                </li>
                <li class="bilingual">
                    <span class="zh">如果没有悬停，则选择最近交互过的媒体</span>
                    <span class="en">If no hover, selects most recently interacted media</span>
                </li>
                <li class="bilingual">
                    <span class="zh">如果有多个播放中的媒体，优先选择视口内最大的</span>
                    <span class="en">If multiple playing media, prioritizes largest in viewport</span>
                </li>
            </ul>
            
            <h4 class="bilingual">
                <span class="zh">网页全屏模式特殊功能</span>
                <span class="en">Fullscreen Mode Special Features</span>
            </h4>
            <ul>
                <li class="bilingual">
                    <span class="zh">在网页全屏模式下，可使用方向键控制视频：</span>
                    <span class="en">In fullscreen mode, you can use arrow keys to control video:</span>
                </li>
                <li class="bilingual">
                    <span class="zh">← 左方向键：快退5秒</span>
                    <span class="en">← Left Arrow: Rewind 5 seconds</span>
                </li>
                <li class="bilingual">
                    <span class="zh">→ 右方向键：快进5秒</span>
                    <span class="en">→ Right Arrow: Forward 5 seconds</span>
                </li>
                <li class="bilingual">
                    <span class="zh">↑ 上方向键：增加音量</span>
                    <span class="en">↑ Up Arrow: Increase volume</span>
                </li>
                <li class="bilingual">
                    <span class="zh">↓ 下方向键：减小音量</span>
                    <span class="en">↓ Down Arrow: Decrease volume</span>
                </li>
                <li class="bilingual">
                    <span class="zh">ESC键：退出网页全屏模式</span>
                    <span class="en">ESC key: Exit fullscreen mode</span>
                </li>
            </ul>
            
            <h4 class="bilingual">
                <span class="zh">播放/暂停控制</span>
                <span class="en">Play/Pause Control</span>
            </h4>
            <p class="bilingual">
                <span class="zh">播放/暂停功能使用浏览器和视频播放器的原生快捷键（通常是空格键），无需额外设置。插件不会拦截或覆盖这些原生控制键。</span>
                <span class="en">Play/pause functionality uses the browser's and video player's native shortcuts (usually spacebar), no additional setup required. The extension will not intercept or override these native controls.</span>
            </p>
            
            <h4 class="bilingual">
                <span class="zh">注意事项</span>
                <span class="en">Notes</span>
            </h4>
            <p class="bilingual">
                <span class="zh">插件的快捷键只在有视频或音频元素可用时才会拦截。如果页面上没有媒体元素，快捷键会正常传递给页面。</span>
                <span class="en">Shortcuts are only intercepted when video or audio elements are available. If no media elements are on the page, shortcuts will pass through normally.</span>
            </p>
            <p class="bilingual">
                <span class="zh">在网页全屏模式下，您可以按ESC键退出全屏。</span>
                <span class="en">In page fullscreen mode, you can press the ESC key to exit fullscreen.</span>
            </p>
        </div>
    </div>

    <script src="options.js"></script>
</body>
</html>