/* --- Speed Indicator --- */
#video-speed-indicator {
    position: fixed; /* Use fixed positioning to stay in viewport */
    top: 20px;
    left: 20px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    font-size: 16px;
    font-weight: bold;
    z-index: 2147483647; /* Max z-index */
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none; /* Ignore mouse events */
}

#video-speed-indicator.visible {
    opacity: 1;
}

/* --- Options Page --- */
.shortcut-input {
    width: 120px;
}

/* 快捷键警告样式 */
.shortcut-input.warning {
    border: 2px solid #ff5252;
    background-color: #ffecec;
}

/* 重置按钮样式 */
#reset-shortcuts:hover {
    background-color: #5a6268 !important;
}

/* --- Lightbox Fullscreen --- */
#vsc-lightbox-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background-color: #000 !important;
    z-index: 2147483646 !important; /* Just below the speed indicator */
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

.vsc-lightbox-video {
    width: 100% !important;
    height: 100% !important;
    max-width: 100% !important;
    max-height: 100% !important;
    object-fit: contain !important;
    /* Remove any borders or shadows the original video might have had */
    border: none !important;
    box-shadow: none !important;
}

.vsc-body-lock {
    overflow: hidden !important;
}